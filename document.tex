%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% This project aims to create the UFC template for presentation.                %%
%% author: <PERSON><PERSON><PERSON><PERSON> - Doctoral student in Computer Science (MDCC)   %%
%% contacts:                                                                     %%
%%    e-mail: <EMAIL>                                                    %%
%%    linktree: https://linktr.ee/maumneto                                       %%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\documentclass{libs/ufc_format}
%\usepackage{transparent}
% Inserting the preamble file with the packages
\input{libs/preamble.tex}
\usepackage{makecell}
%\usepackage{animate}
\usepackage{graphicx}								%Permite se inserir gráficos
\usepackage{tikz}
%% MATH
\usepackage{commath}	                        	%permite o uso de \norm{} (norma) e de \abs{} (módulo)
\usepackage{amsmath, amsfonts, amssymb, esint}	    %Escritas do modo math
\usepackage{bm} 									%Negrito para modo math greek letters
\usepackage{steinmetz}		                        %Fase /____
\usepackage{mathtools}                              %For matrix align (deixar sempre acima de mathabx para evitar erro \underbrace)
\usepackage{mathabx}		                        %convolution \Asterisk
\usepackage{wrapfig}


\DeclareMathOperator*{\minim}{\text{minimize}}

% Inserting the references file
%\addbibresource{references.bib}
%\bibliographystyle{configs/IEEEbib}
\bibliography{configs/references.bib}
\renewcommand*{\bibfont}{\normalfont\scriptsize}

%% Math Operators
\DeclareMathOperator{\E}{\mathbb{E}}
\DeclareMathOperator{\opt}{opt}

% Declaração de Comandos de Texto
\newcommand{\bizu}[1]{\textcolor{red}{\textbf{#1}}}
\newcommand{\unit}[1]{$[\text{\textit{#1}}]$}

%Font Equations
\usefonttheme{professionalfonts}

% Title
\title[Dissertação de Mestrado: SONAR Cognitivo]{\huge\textbf{\makecell{\includegraphics[scale=.3]{./fotos/PEE.png}\includegraphics[scale=.75]{./fotos/Logo_COPPE_-_UFRJ.png}\\ \textit{Defesa de Mestrado}}}}


% Subtitle
\subtitle{Aplicação de Arquitetura Cognitiva Ciclo Percepção-Ação a um Sistema de SONAR Ativo Monoestático: conceitos, formulação teórica e simulações computacionais.}
% Author of the presentation
%\author{1T(EN) Alcântara}
% Institute's Name
\institute[\textbf{COPPE/UFRJ - Eng. Elétrica}]{
    % email for contact
    \vspace{-1.3cm}

    \makecell{Caio César Marques Pereira de Alcântara\\ \email{<EMAIL>}%\\
    		% Fábio Oliveira: \email{<EMAIL>}\\
    		% Prof. Natanael Moura Jr: \email{<EMAIL>}\\
    		% Nelson Monnerat: \email{<EMAIL>}\\
    		% Fernando Monteiro: \email{<EMAIL>}\\
    		% Vinícius Ávila: \email{<EMAIL>}
			}
			
	\makecell[l]{\\Banca Examinadora:\\
		\begin{tabular}{ll}
			Prof. Dr. Natanael Nunes de Moura Junior (Orientador) & LPS / PEE / EP / UFRJ \\
			Prof. Dr. Paulo Sérgio Ramirez Diniz & SMT / PEE / EP / UFRJ\\
			Prof. Dr. Carlos Eduardo Parente Ribeiro & LIOc / PENO / EP / UFRJ\\
			Profa. Dra. Maria das Dores dos Santos Miranda & LCS / PTC / EP / USP
		\end{tabular}
	}
%\makecell{\email{<EMAIL>}}
%    \newline
%    % Department Name
%	\vspace*{-.2cm}
%    \department{\color{white}.}
%    \newline
%    % university name
%    \ufc
}
% date of the presentation
\vspace*{-1.cm}
\date{\today}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Start Document of the Presentation                                           %%               
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{document}
% insert the code style
\input{libs/code_style}

%% ---------------------------------------------------------------------------
% First frame (with tile, subtitle, ...)
\begin{frame}{}
    \maketitle
\end{frame}

%% ---------------------------------------------------------------------------
% Second frame
\begin{frame}{Sumário}
	\vspace{-1.2cm}
    \begin{multicols}{2}
        \tableofcontents
        \vphantom{
        	\makecell{a%\\       	
%        	b%\\
%        	c\\
%        	d\\
%        	e
        	}
        	}
    \end{multicols}
\end{frame}

%% ---------------------------------------------------------------------------
\section{Introdução}
\subsection{Motivação}
\begin{frame}{Introdução: Motivação}
	\begin{overlayarea}{\textwidth}{.45\textheight}
		\centering
		\invisible<2-|handout:1>{
			\begin{multicols}{2}
			\tableofcontents[
			sectionstyle=show/shaded,
			subsectionstyle=show/show/shaded,
			subsubsectionstyle=show/show/shaded
			]
			\end{multicols}
		}
	\end{overlayarea}%
	\begin{overlayarea}{\textwidth}{1.0\textheight}
		\centering
		\visible<2-4>{
			\vspace{-4.4cm}
			\begin{block}{Contexto}
				\begin{itemize}
					\item Amazônia Azul \cite{amazonia_azul} Zona Econômica Exclusiva (ZEE) \cite{ZEE}
					\begin{itemize}
						\item Pré-sal: $1,\!9e6$ bpd (03/2020) \cite{pre_sal} {\color{blue}(Arab. $9e6$ bpd (10/2023)\cite{ibp})}
						\item Rotas Marítimas: $57,\!4\%$ em exportação \cite{importacao_FGV} e mais de $90\%$ da importação \cite{exportacao_FGV}.
					\end{itemize}
					\item Soberania Nacional e Salva-guarda das riquezas nacionais
					\begin{itemize}
						\item MB-PEM2040: Atividade P\&D $\Rightarrow$ vertente estratégica
						\item AEN: Fortalecimento do Poder Naval $\Rightarrow$ P\&D tecnologias-chave
					\end{itemize}
				\end{itemize}
			\end{block}
		}
		\visible<3-4>{
			\vspace{-0.5cm}
			\begin{block}{Ameaças externas de Contestação à Soberania Nacional}
				\begin{itemize}
					%					\item Via terrestre
					%					\begin{itemize}
						%						\item Tropas, carros de combate, obseiros, lançadores de mísseis
						%					\end{itemize}
					%					\item Via áerea
					%					\begin{itemize}
						%						\item Bombardeiros e Aviação de caça
						%					\end{itemize}
					\item Via marítima
					\begin{itemize}
						\item Destróiers, Porta-aviões e \bizu{Submarinos}.
					\end{itemize}
				\end{itemize}
			\end{block}
		}
		\visible<4-4>{
			\vspace{-0.5cm}
			\begin{alertblock}{Observações}
				\begin{itemize}
					\item Destróiers e Porta-aviões: detecção facilitada \cite{sar01,sar02}
					\begin{itemize}
						\item Há armamentos os quais eles não possuem defesa hoje em dia: saturação por drones, mísseis hiperssônicos.
					\end{itemize}
					\item Submarinos são muito furtivos e difíceis de serem detectados quando abaixo da linha d'água.
				\end{itemize}
			\end{alertblock}
		}
	\end{overlayarea}
\end{frame}

%% ---------------------------------------------------------------------------
\subsection{Objetivos}
\begin{frame}{Introdução: Objetivos}
	\begin{block}{Contexto almejado}
		\begin{itemize}
			\item Busca por Submarinos ocultos (ASW)
			\begin{itemize}
				\item Utilizando Navios de Escolta
				\begin{itemize}
					\item SONAR Ativo Monoestático (SSAM; HMS)
				\end{itemize}
			\end{itemize}
		\end{itemize}
	\end{block}
	\begin{alertblock}{Objetivos}
		\begin{itemize}
			\item Aumentar o desempenho de SSAMs para ASW ($\uparrow$ precisão $\Rightarrow$ $\downarrow \bm{e}^T\bm{e}$ $\Rightarrow$ $\uparrow$ SNR)\footnote{$\bm{e}=\bm{e}[n\!\ |\!\ n] = \bm{x}[n]-\bm{\hat{x}}[n\!\ |\!\ n]$}. Possibilidades:
			\pause
			\begin{itemize}
				\item Filtros Bayesianos (aprox)
				\item Modelagem acústica submarina (formatação de pulsos)
				\item \bizu{Sistemas Cognitivos}
			\end{itemize}
		\end{itemize}
	\end{alertblock}
\end{frame}

%% ---------------------------------------------------------------------------
\subsection{Arquiteturas Cognitivas}
\begin{frame}{Introdução: Arquiteturas Cognitivas}
	\vspace*{-.1cm}
	\footnotesize
	\begin{itemize}
		\item Há diversas Arquiteturas Cognitivas \cite{25,Iulila_cognitive-architecture_2020}
		\vspace*{-.08cm}
		\begin{itemize}
			\footnotesize
			\item Cada uma com seu respectivo compromisso por aplicação
		\end{itemize}
	\end{itemize}
	\vspace*{-.1cm}
	\hspace*{.2cm}\includegraphics[scale=.32]{./fotos/soar.png} \hspace*{.4cm}\includegraphics[scale=.36]{./fotos/icarus.png}\\
	\vspace*{-.2cm}
	\pause
	\begin{block}{Arquitetura Escolhida para o início do P\&D}
		Arquitetura Cognitiva proposta por \citet{haykin2006cognitive}: \bizu{Ciclo Percepção-Ação Básica}
	\end{block}
	\hspace*{2.cm}\includegraphics[scale=.4]{./fotos/cycle-percept.png}
\end{frame}

%% ---------------------------------------------------------------------------
\subsection{Contribuições}
\begin{frame}{Introdução: Contribuições}
	\begin{block}{Principais Contribuições}
		\begin{itemize}
			\item Aplicação de Arquitetura Cognitiva \bizu{Ciclo Percepção-Ação Básica} num SSAM
			\begin{itemize}
				\item Implementação modular.
				\item Intuito de melhorar o desempenho na atividade de ASW.
			\end{itemize}
		\end{itemize}
	\end{block}
	\hspace*{1.2cm}\includegraphics[scale=.9]{./fotos/TikZ_cognitive_basic.pdf}
\end{frame}

%% ---------------------------------------------------------------------------
% This presentation is separated by sections and subsections
\section{Revisão}
\subsection{Sistemas de SONAR}
\begin{frame}{Sistemas SONAR}
	\begin{overlayarea}{\textwidth}{.45\textheight}
		\centering
		\invisible<2-|handout:1>{
			\begin{multicols}{2}
			\tableofcontents[
			sectionstyle=show/shaded,
			subsectionstyle=show/shaded,
			subsubsectionstyle=show/show/shaded
			]
		\end{multicols}
		}
	\end{overlayarea}%
	\begin{overlayarea}{\textwidth}{1.0\textheight}
		\centering
		\visible<2-2>{
			\vspace{-4.1cm}
			\begin{exampleblock}{Física Básica: Propagação no mar}
				\begin{itemize}
					\item Ondas elétromagnéticas não se propagam eficientemente
					\item Uso de ondas mecânicas
					\begin{itemize}
						\item Campo escalar de Pressão Acústica
					\end{itemize}
				\end{itemize}
			\end{exampleblock}
%			\vspace{-.3cm}
			\begin{block}{Tipos}
				\begin{itemize}
					\item Passivo
					\begin{itemize}
						\item Apenas escuta
						\item Processa os sinais das fontes acústicas no meio
					\end{itemize}
					\item \bizu{Ativo}
					\begin{itemize}
						\item Emite pulso/sinal acústico
						\item Escuta e processa os ecos
					\end{itemize}
				\end{itemize}
			\end{block}
%			\vspace{-.1cm}
%			\pause
%			\pause
%			\pause
%			\includegraphics[scale=.4]{./fotos/passivo.png}%
%			\pause
%			\hspace*{.3cm}\includegraphics[scale=.2]{./fotos/tx_rx.png}
		}
	\end{overlayarea}
%
%	\pause
%	\noindent
%	\hspace{-.5cm}
%	\begin{table}[H]
%		\centering
%		\small
%		\begin{tabular}{|c||c|c|c|c|}
%			\hline
%				& \multicolumn{4}{|c|}{Escalões de Manutenção} \\
%				& Primeiro & Segundo & Terceiro & Quarto\\\hline\hline
%				Software &	&	&	& \\\hline
%				Hardware &	&	&	& \\
%			\hline
%		\end{tabular}
%%		\caption{Tabela de Valores Medidos e Inferidos na Experiência 01}
%%		\label{tab:exp01_medicoes}
%	\end{table}
	
\end{frame}
%% ---------------------------------------------------------------------------

\begin{frame}{Sistemas SONAR - Passivo}
	\centering
	\includegraphics[scale=.7]{./fotos/passivo.png}%
	
	Fonte: \cite{bozzi}
\end{frame}
%% ---------------------------------------------------------------------------

\begin{frame}{Sistemas SONAR - Ativo}
	\centering
	\hspace*{-.3cm}\includegraphics[scale=.33]{./fotos/tx_rx.png}\\
	\vspace*{-1.5cm}
%	\pause
%	\hspace*{-.0cm}\includegraphics[scale=.4]{./fotos/antisub.jpg}
\end{frame}

%% ---------------------------------------------------------------------------
\subsubsection{SONAR Ativo}
\begin{frame}{SONAR Ativo}
	\begin{block}{Quanto ao sensoriamento}
		\begin{itemize}
			\item Multiestático \cite{Haykin, arulampalam} %colocar ref do Arulampalam
			\item \bizu{Monoestático}
		\end{itemize}
	\end{block}
	\vspace*{-.3cm}
	\pause
	\begin{block}{Quanto ao propósito (alguns exemplos)}
		\begin{itemize}
			\item Imageamento do leito oceânico
			\item Detecção de Mergulhadores
			\item \bizu{Detecção de Submarinos}
		\end{itemize}
	\end{block}
%	\vspace*{-.3cm}
	\pause
	\includegraphics[scale=.5]{./fotos/sidescan.png}%
	\hspace*{.2cm}\includegraphics[scale=.4]{./fotos/asw02.png}
%	\begin{exampleblock}{Position-Dependent Noise Kalman Filtering (PDNKF)}
%		\begin{itemize}
%			\item ``Lineariza'' o modelo de observações.
%			\item Estima o ruído (Wold's Decomposition) Não-AWGN.
%		\end{itemize}
%	\end{exampleblock}
\end{frame}

%% ---------------------------------------------------------------------------
\subsection{Formatação de Pulsos}
\begin{frame}{Formatação de Pulsos}
	\begin{overlayarea}{\textwidth}{.45\textheight}
		\centering
		\invisible<2-|handout:1>{
			\begin{multicols}{2}
			\tableofcontents[
			sectionstyle=show/shaded,
			subsectionstyle=show/shaded,
			subsubsectionstyle=show/show/shaded
			]
		\end{multicols}
		}
	\end{overlayarea}%
	\begin{overlayarea}{\textwidth}{1.0\textheight}
		\centering
		\visible<2-2>{
			\vspace{-4.4cm}
			\begin{gather}
				s_{\text{Tx}}(t) =  \sqrt{2}\mathfrak{Re}\left(\sqrt{E_{\text{Tx}}}a(t)e^{j\varphi(t)}e^{j2\pi f_c t}\Pi\left((t-0.5t_p)/t_p\right)\right)
				\label{eq:signal_TX}\\
				s_{\text{E}}(t) = \sqrt{2}\mathfrak{Re}\left(\sqrt{E_{\text{Rx}}}a(t-\tau)e^{j\varphi(t-\tau)}e^{j2\pi (f_c+\delta{}f) (t-\tau)}\Pi\left((t-\tau-0.5t_p)/t_p\right)\right)
				\label{eq:signal_E}\\
				s_{\text{Rx}}(t) = s_{\text{E}}(t) + n(t) %vai ter dependência em r e em \phi
				\label{eq:signal_RX}\\
				n(t) = n_R(t|\bm{\theta}) + \nu(t|\bm{\theta})
				\label{eq:signal_noise}
			\end{gather}
			\hspace*{-.2cm}
			\includegraphics[scale=.85]{./fotos/TikZ_range.pdf}%
			\includegraphics[scale=.85]{./fotos/TikZ_bearing.pdf}
		}
	\end{overlayarea}	
\end{frame}

%% ---------------------------------------------------------------------------
\subsubsection{Função Ambiguidade}
\begin{frame}{Formatação de Pulsos - AF}
	\begin{gather}
		\abs{\text{\textit{AF}}(\delta{}f,\tau)} \approx \abs{\mathcal{F}\left(\Tilde{s}(t) \overline{\Tilde{s}}\left(t-\tau\right)\right)\!\!(\delta\!{}f)}
		\label{eq:AF_narrowband_low-doppler}\\
		\abs{\text{\textit{AF}}(\delta{}f,\tau)} \approx \abs{\int_{\mathbb{R}} \Tilde{s}(f,t) \overline{\Tilde{s}}\left(f-\delta\!{}f, t-\tau\right)\ dt}
		\label{eq:AF_another_interpre}
	\end{gather}
	%	\begin{block}{Quanto ao sensoriamento}
		%		\begin{itemize}
			%			\item Multiestático \cite{Haykin, arulampalam} %colocar ref do Arulampalam
			%			\item \bizu{Monoestático}
			%		\end{itemize}
		%	\end{block}
	%	\vspace*{-.3cm}
	%	\pause
	%	\begin{block}{Quanto ao propósito (alguns exemplos)}
		%		\begin{itemize}
			%			\item Imageamento do leito oceânico
			%			\item Detecção de Mergulhadores
			%			\item \bizu{Detecção de Submarinos}
			%		\end{itemize}
		%	\end{block}
	\hspace*{0cm}
	\includegraphics[scale=.41]{./fotos/matlab_CW_AF_full.png}
\end{frame}

%% ---------------------------------------------------------------------------
\subsubsection{Fun.Transf e Ruído}
\begin{frame}{Formatação de Pulsos - FT; Ruído }
	\centering
	\includegraphics[scale=.8]{./fotos/TikZ_TransferFunction_Environment.pdf}%
	\hspace*{.5cm}$\nu_y|\bm{\theta} \sim \mathcal{N}(0,\sigma^2(\bm{\theta}))$
	
	\includegraphics[scale=.33]{./fotos/ruido00.png}%
	\includegraphics[scale=.55]{./fotos/ruido01.png}
	
	\includegraphics[scale=.41]{./fotos/ruido02.png}
\end{frame}

%% ---------------------------------------------------------------------------
\subsection{Bloco de Recepção}
\subsubsection{Correlator}
\begin{frame}{Bloco de Recepção }
	\begin{overlayarea}{\textwidth}{.45\textheight}
		\centering
		\invisible<2-|handout:1>{
			\begin{multicols}{2}
			\tableofcontents[
			sectionstyle=show/shaded,
			subsectionstyle=show/shaded,
			subsubsectionstyle=show/show/shaded
			]
		\end{multicols}
		}
	\end{overlayarea}%
	\begin{overlayarea}{\textwidth}{1.0\textheight}
		\centering
		\visible<2-2>{
			\vspace{-4.1cm}
			\includegraphics[scale=.6]{./fotos/TikZ_BD_sonar_rx.pdf}
			
			\begin{block}{Correlator}
				\begin{equation}
					R_{sq}(\tau) = \left(g_{-1}(\overline{s})\asterisk q\right)\!(\tau)
					\label{eq:corr_em_conv}
				\end{equation}
				onde $s(t)=s_{\text{Tx}}(t)$ e $q(t)=s_{\text{Rx}}(t)$
			\end{block}
			
			\includegraphics[scale=.4]{./fotos/corr_full.png}
%			\begin{align}
%				\bm{x}[n+1] &= \bm{A}[n]\ \bm{x}[n] + \bm{\nu}_x[n]\label{eq:Processos}\\
%				\bm{y}[n] &= \bm{H}[n]\ \bm{x}[n] + \bm{\nu}_y[n]\label{eq:Observacao}
%			\end{align}
		}
	\end{overlayarea}
\end{frame}
%% ---------------------------------------------------------------------------
\subsubsection{Beamforming}
\begin{frame}{Bloco de Recepção - BF}
	\centering
	\includegraphics[scale=.45]{./fotos/bf_array.png}%
	\includegraphics[scale=.35]{./fotos/bf.png}
	
	\includegraphics[scale=.35]{./fotos/bf02.png}
\end{frame}

%% ---------------------------------------------------------------------------
\subsubsection{Filt. Kalman}
\begin{frame}{Bloco de Recepção - KF}
	
	\centering
	\includegraphics[scale=.8]{./fotos/TikZ_kalman_standard_BD.pdf}
	
	\vspace*{-.5cm}
	\begin{equation}
		\begin{array}{r@{}l}
			\bm{P}[1|0] &= \delta \bm{I}\\
			\bm{\hat{x}}\left[1|0\right] &= \bm{0}
		\end{array}
		\label{eq:KalmanBlindInit}
	\end{equation}
	
	\begin{equation}
		\begin{array}{r@{}l}
			\bm{S}[n] &= \bm{H}[n]\ \bm{P}\left[n|n-1\right]\ \bm{H}^T[n] + \bm{R}[n]\\
			\bm{K}[n] &= \bm{P}\left[n|n-1\right]\ \bm{H}^T[n]\ \bm{S}^{-1}[n]\\
			\bm{P}\left[n|n\right] &= \left( \bm{I} - \bm{K}[n]\ \bm{H}[n] \right) \bm{P}\left[n|n-1\right]\\
			\bm{\alpha}[n] &= \bm{y}[n] - \bm{H}[n]\ \bm{\hat{x}}\left[n|n-1\right]\\
			\bm{\hat{x}}\left[n|n\right] &= \bm{\hat{x}}\left[n|n-1\right] + \bm{K}[n]\ \bm{\alpha}[n]
		\end{array}
		\label{eq:KalmanPosterior}
	\end{equation}
	
	\begin{equation}
		\begin{array}{r@{}l}
			\bm{P}\left[n+1|n\right] &= \bm{A}[n]\ \bm{P}\left[n|n\right]\ \bm{A}^T[n] + \bm{Q}[n]\\
			\bm{\hat{x}}\left[n+1|n\right] &= \bm{A}[n]\ \bm{\hat{x}}\left[n|n\right]
		\end{array}
		\label{eq:KalmanPrior}
	\end{equation}
\end{frame}

%% ---------------------------------------------------------------------------
\section{Método Proposto}
\subsection{Revisão Prog. Din.}
\begin{frame}{Método Proposto}
	\begin{overlayarea}{\textwidth}{.45\textheight}
		\centering
		\invisible<2-|handout:1>{
			\begin{multicols}{2}
			\tableofcontents[
			sectionstyle=show/shaded,
			subsectionstyle=show/show/shaded,
			subsubsectionstyle=show/show/shaded
			]
		\end{multicols}
		}
	\end{overlayarea}%
	\begin{overlayarea}{\textwidth}{1.0\textheight}
		\centering
		\visible<2-2>{
			\vspace{-4cm}
			\begin{itemize}
				\item Implementação de CA Percepção-Ação Básica em SSAM.
			\end{itemize}
			\includegraphics[scale=.8]{./fotos/TikZ_cognitive_basic.pdf}
			\vspace{-.1cm}
			\begin{exampleblock}{Cognitivo}
				\begin{itemize}
					\item Sinais filtrados: Estatísticas do Filtro Bayesiano
					\item Decisão: Cognitive Waveform Selection (CWS)
					\begin{itemize}
						\item ADP baseada nas Estatísticas
					\end{itemize}
				\end{itemize}
			\end{exampleblock}
			\begin{block}{Programação Dinâmica}
				\begin{itemize}
					\item Princípio de Optimização: Bellman
					\begin{itemize}
						\item Fatora um problema de tomada de decisão: multiestágio para $L$ estágios únicos.
					\end{itemize}
				\end{itemize}
			\end{block}
			
		}
	\end{overlayarea}
\end{frame}


%% ---------------------------------------------------------------------------

\begin{frame}{Revisão DP - Info. de estado perfeita}
	\includegraphics[scale=.7]{./fotos/TikZ_BD_dp_full.pdf}%
	\includegraphics[scale=.7]{./fotos/TikZ_BD_dp_Bellman.pdf}
	\vspace*{-.3cm}
	\begin{equation}
		\resizebox{1\textwidth}{!}{$
		\begin{split}
			&J_{\pi}^\asterisk\left(\bm{x}[0]\right) =\\
			&\qquad\mathop{\opt}\limits_{\pi \in \mathcal{P}_{\bm{\theta}}[L\!-\!1]} \underbrace{\mathop{\E}\limits_{\mathop{\left\lbrace\bm{\nu_x}[k]\right\rbrace}\limits_{k=0,...,L\!-\!1}}\left( \left.\sum_{k=0}^{L\!-\!1}\gamma^k g_k\!\left(\bm{x}[k], \mu_k\!\left(\bm{x}[k]\right), \bm{\nu_x}[k]\right) + \gamma^L g_L\!\left(\bm{x}[L]\right)\right| \bm{x}[0], \bm{\theta}_{[L\!-\!1]}\right)}_{\text{Função Custo/Recompensa acumulado(a) esperado(a) $J_\pi\!\left(\bm{x}[0]\right)$}}%
			\label{eq:dp_optimalidade_basic_full}
		\end{split}
	$}
	\end{equation}
	\vspace*{-.3cm}
	\begin{equation}
		\resizebox{.9\textwidth}{!}{$
		\left\lbrace\begin{matrix*}[l]
			J_{L}\left(\bm{x}[L]\right) = g_L\left(\bm{x}[L]\right)\\
			%
			J_{k}^\asterisk\left(\bm{x}[k]\right) = \mathop{\opt}\limits_{\bm{\theta}[k] \in \mathcal{P}_{\bm{\theta}}(\bm{x}[k])} \mathop{\E}\limits_{\bm{\nu_x}[k]}\left(\vphantom{J_{k+1}\left.\left(f_k\!\left(\bm{x}[k], \bm{\theta}[k], \bm{\nu_x}[k]\right) \right)\right|}%
			g_k\!\left(\bm{x}[k], \bm{\theta}[k], \bm{\nu_x}[k]\right)\right.\\
			\qquad\qquad\qquad\qquad\qquad\quad + \left.\gamma J_{k+1}\left.\left(f_k\!\left(\bm{x}[k], \bm{\theta}[k], \bm{\nu_x}[k]\right) \right)\right| \bm{x}[k], \bm{\theta}[k]\right)
		\end{matrix*}\right.
		\label{eq:dp_bellman_eq_basic}
	$}
	\end{equation}
\end{frame}

%% ---------------------------------------------------------------------------

\begin{frame}{Revisão DP - Info. de estado imperfeita}
	\begin{equation}
		\bm{y}[k] = h_k\!\left(\bm{x}[k], \bm{\theta}[k\!-\!1], \bm{\nu_y}[k]\right)
		\label{eq:dp_imperfet_obs}
	\end{equation}
	\begin{equation}
		\bm{I}[k\!+\!1]=(\bm{I}[k], \bm{\theta}[k], \bm{y}[k\!+\!1]),\ k=0,1,...,L\!-\!2
		\label{eq:well-defined_state02_new}
	\end{equation}
	\begin{equation}
		\hspace*{-.8cm}
		\resizebox{1.1\textwidth}{!}{$
		\left\lbrace\begin{matrix*}[l]
			J_{L\!-\!1}^{\asterisk}\!\left(\bm{I}[L\!-\!1]\right) =\!\!\! \mathop{\opt}\limits_{\bm{\theta}[L\!-\!1] \in \mathcal{P}_{\bm{\theta}}(\bm{x}[L\!-\!1])} \mathop{\E}\limits_{\bm{x}[L\!-\!1],\bm{\nu_x}[L\!-\!1]} \left(\vphantom{g_L\left(f_{L\!-\!1}\left(\bm{x}[L\!-\!1], \bm{\theta}[L\!-\!1], \bm{\nu_x}[L\!-\!1]\right)\right)}%
			\right.\vphantom{g_L\left(f_{L\!-\!1}\left(\bm{x}[L\!-\!1], \bm{\theta}[L\!-\!1], \bm{\nu_x}[L\!-\!1]\right)\right)}%
			g_L\left(f_{L\!-\!1}\left(\bm{x}[L\!-\!1], \bm{\theta}[L\!-\!1], \bm{\nu_x}[L\!-\!1]\right)\right)\\
			\qquad\qquad\qquad\qquad+ \left.g_{L\!-\!1}\left(\bm{x}[L\!-\!1], \bm{\theta}[L\!-\!1], \bm{\nu_x}[L\!-\!1]\right)\ \right| \bm{I}[L\!-\!1], \bm{\theta}[L\!-\!1]\ \left.\vphantom{g_L\left(f_{L\!-\!1}\left(\bm{x}[L\!-\!1], \bm{\theta}[L\!-\!1], \bm{\nu_x}[L\!-\!1]\right)\right)}%
			\right)\\
			%        %
			J_{k}^\asterisk\left(\bm{I}[k]\right) = \mathop{\opt}\limits_{\bm{\theta}[k] \in \mathcal{P}_{\bm{\theta}}(\bm{x}[k])} \mathop{\E}\limits_{\bm{x}[k],\bm{\nu_x}[k],\bm{y}[k\!+\!1]}\left(\vphantom{g_L\left(f_{L\!-\!1}\left(\bm{x}[L\!-\!1], \bm{\theta}[L\!-\!1], \bm{\nu_x}[L\!-\!1]\right)\right)}
			\right. g_k\left(\bm{x}[k], \bm{\theta}[k], \bm{\nu_x}[k]\right)\\
			\qquad\qquad\qquad\qquad\qquad\quad + \gamma \left.J_{k+1}\left(\bm{I}[k], \bm{\theta}[k], \bm{y}[k\!+\!1]\right)\ 
			\right| \bm{I}[k], \bm{\theta}[k]\left.\vphantom{g_L\left(f_{L\!-\!1}\left(\bm{x}[L\!-\!1], \bm{\theta}[L\!-\!1], \bm{\nu_x}[L\!-\!1]\right)\right)}%
			\right)
		\end{matrix*}\right.
		$}
		\label{eq:dp_bellman_imperfect-info_case}
	\end{equation}
\end{frame}

%% ---------------------------------------------------------------------------
\subsection{Descrição Teórica}
\begin{frame}{Descrição Teórica}
	\begin{block}{Modelos de Estados e de Sensor}
	\vspace*{-.3cm}
	\begin{minipage}{.5\textwidth}
		\centering
		\begin{align*}
			\bm{x}[n+1] &= \bm{F}\bm{x}[n] + \bm{\nu_x}[n]\\
			\bm{y}[n] &= \bm{h}(\bm{x}[n]) + \bm{\nu_y}[n|\bm{\theta}]
		\end{align*}
		\vspace*{.1cm}
	\end{minipage}%
	\begin{minipage}{.5\textwidth}
		\centering
		\includegraphics[width=0.6\textwidth]{./fotos/TikZ_sonar_coordinates.pdf}	
	\end{minipage}	
	\end{block}
	\vspace*{-.2cm}
	\begin{block}{Bloco de Recepção}
		\begin{itemize}
			\item Filtro Bayesiano: KF como estimador recursivo
			\begin{itemize}
				\item Estatística das estimativas $\bm{\hat{x}}[n\!+\!1|n\!+\!1]\ |\ \bm{\nu_y}[n|\bm{\theta}]$
			\end{itemize}
		\end{itemize}
	\end{block}
	\vspace*{-.2cm}
	\begin{alertblock}{Bloco Cognitivo}
		\begin{itemize}
			\item Horizonte $L=1$
			\begin{itemize}
				\item $\bm{I}[0] = \bm{y}[0]$
				\item $J_{0}(\bm{y}[0]) = \mathop{\mathbb{E}}_{\bm{x}[0],\bm{\nu_x}[0]}\left(%
					\left. g_0\left(\bm{x}[0], \bm{\theta}[0], \bm{\nu_x}[0]\right)\ \right|\ \bm{y}[0], \bm{\theta}[0] %
					\right)$
				\begin{itemize}
					\item $g_1(\cdot) = 0$, (condição terminal usual \cite{brandimarte2021shortest})
				\end{itemize}
			\end{itemize}
		\end{itemize}
	\end{alertblock}
	
	
\end{frame}

%% ---------------------------------------------------------------------------
\begin{frame}{Descrição Teórica}
	
		\begin{alertblock}{Bloco Cognitivo}
		\begin{itemize}
			\item $\bm{\theta}^*[0] = \arg\mathop{\opt}\limits_{\bm{\theta}[0]\in\mathcal{P}_{\bm{\theta}}} J_0(\bm{y}[0])$
			\item $g_0(\cdot)$ baseada em $\bm{e}[1|1]$
			\begin{itemize}
				\item $g_0(\bm{x}[0], \bm{\theta}[0], \bm{\nu_x}[0]) = \bm{e}^T[1|1]\bm{e}[1|1]$
				\item $J_{0}(\bm{y}[0]) = \mathop{\mathbb{E}}_{\bm{x}[0],\bm{\nu_x}[0]}\left(%
				\left. \bm{e}^T[1|1]\bm{e}[1|1]\ \right|\ \bm{y}\left[0\!\ |\!\ \bm{\theta}[0]\right], \bm{\theta}[0] %
				\right)$
				\begin{itemize}
					\item $J_0(\bm{y}[0])\approx \text{Tr}(\bm{P}[1\!\ |\!\ 1, \bm{\theta}[0]])$ \ \cite{artigo_base_CR,xue_CR}, para modelo conhecido \cite{artigo_base_CR}.
				\end{itemize}
			\end{itemize}
			\item ADP - CWS: 
				$\boxed{\theta^{\asterisk}[0] = \arg\mathop{\min}_{ \bm{\theta}[0]\in\mathcal{P}_{\bm{\theta}}} \text{Tr}\left(\bm{P}[1\!\ |\!\ 1,\bm{\theta}[0]]\right)}$
		\end{itemize}
	\end{alertblock}
\end{frame}

%% ---------------------------------------------------------------------------
\subsection{Formas de Avaliação}
\begin{frame}{Formas de Avaliação}
	\begin{exampleblock}{\textbf{Formas de Avaliação}}
		\begin{itemize}
			\item Comparação entre o SSAMs Convencional e Cognitivo
			\begin{itemize}
				\item Monte-Carlo
				\item RMSE em [\textit{dB}] e relativo à $\mathbb{V}(\bm{\nu_y'}\left[n\!\ |\!\  \bm{\theta}[n]\right])$
			\end{itemize}
			\item Verificação $\bm{\theta}^{\asterisk}[n]$ em uma realização do Cognitivo
			\item Dois cenários
			\item Filtros de Kalman utilizados:
			\begin{itemize}
				\item KF, PDNKF, EKF e UKF, conforme \cite{alcantara_oceans}
			\end{itemize}
		\end{itemize}
	\end{exampleblock}
\end{frame}

%% ---------------------------------------------------------------------------

\section{Simulações e Resultados}
\subsection{Condições e Modelos}
\begin{frame}{Simulações e Resultados}
	\begin{overlayarea}{\textwidth}{.45\textheight}
		\centering
		\invisible<2-|handout:1>{
			\begin{multicols}{2}
			\tableofcontents[
			sectionstyle=show/shaded,
			subsectionstyle=show/show/shaded,
			subsubsectionstyle=show/shaded
			]
		\end{multicols}
		}
	\end{overlayarea}%
	\begin{overlayarea}{\textwidth}{1.0\textheight}
		\centering
		\visible<2-2>{
			\vspace{-4.3cm}
			\begin{block}{Condições de Simulação}
				\begin{itemize}
					\item Espaço de decisão $\mathcal{P}_{\bm{\theta}}$ (somente pulsos CW)
					\begin{itemize}
						\item $f_c\ \in \ \lbrace 10, 15, 20, 25, 30 \rbrace$ \unit{kHz}
						\item $t_p\ \in \ \lbrace 20, 50, 100, 150, 200 \rbrace$ \unit{ms}
					\end{itemize}
					\item Trajetória retilínea e cond. de modelo de sist.
					\begin{itemize}
						\vspace*{-.4cm}
						\begin{multicols}{2}
							\item $\vec{p}_0 = (-5;5)$\unit{km} $\vec{p}_{N-1} = (5;5)$\unit{km}
							\item $\vec{v} = (5;0)$\unit{m/s}
							\item $c = 1500$\unit{m/s}
							\item $\sigma_v = 0,\!2$\unit{m/s}
							\item $T=10$\unit{s}
						\end{multicols}
					\end{itemize}
					\vspace*{-.4cm}
					\item Computer: Intel i5 8ªgen. 32 GB RAM, 128 MB VRAM, 500 GB SSD, Win.10, MATLAB R2018a
					\item Monte-Carlo $M=100$ realizações
				\end{itemize}
			\end{block}
%			\vspace{-.1cm}
			\begin{block}{Modelos de Simulação, para $\nu_r$ e $\nu_\phi$}
				\vspace{-.4cm}
				\begin{equation}
					\begin{matrix*}[l]
						\sigma(t_p, f_c) = \sqrt{\left(a_{f}^2 \left( f_c - f_{c\text{ min}} \right)^2 + b_f\right)^2 + \left(a_{t}^2 \left( t_p - t_{p\text{ min}} \right)^2 + b_t\right)^2}\\
						\nu\!\ |\!\ t_p, f_c \sim \mathcal{N}\left(0, \sigma^2(t_p, f_c) \right)
					\end{matrix*}
					\label{eq:sim_noise}
				\end{equation}
			\end{block}
			%
%			\vspace{-.2cm}
%			
%			\begin{alertblock}{Filters Parameters}
%				{\color{red}$\alpha$-$\beta$}: $\alpha = 0.5$ and $\beta = 1/6$; {\color{blue}UKF}: $\alpha=0.001$, $\kappa=0$, $\beta=2$, and $L=4$
%			\end{alertblock}
		}
	\end{overlayarea}
\end{frame}

%% ---------------------------------------------------------------------------
\subsection{Cenário 1}
\begin{frame}{Simulações e Resultados: Cenário 1}
	\begin{alertblock}{\textbf{Cenário 1}}
		\scriptsize
		$t_{p \text{ min}}=100$\unit{ms} e $f_{c \text{ min}}=20$\unit{kHz}; $a_f = 2,\!5e\!-\!4$, $a_t = 20$, $b_f = 10$ e $b_t = 10$, para $\sigma_r$ (dado em metros), e $a_f = 2,\!5e\!-\!4$, $a_t = 20$, $b_f = 1,\!5$ e $b_t = 1,\!5$, para $\sigma_{\phi}$ (dado em graus). O \bizu{SSAM convencional}: $f_{c0} = 15$\unit{kHz}, $t_{p0} = 50$\unit{ms}
	\end{alertblock}
	\vspace*{.5cm}
	\hspace*{-1.cm}
	\begin{minipage}{0.57\textwidth}
		\includegraphics[scale=.5]{./fotos/cenario01_sig_r_fc.png}
	\end{minipage}
	\begin{minipage}{0.5\textwidth}
		\includegraphics[scale=.5]{./fotos/cenario01_sig_r_tp.png}
	\end{minipage}
	
	\hspace*{-1.cm}
	\begin{minipage}{0.57\textwidth}
		\includegraphics[scale=.5]{./fotos/cenario01_sig_phi_fc.png}
	\end{minipage}
	\begin{minipage}{0.5\textwidth}
		\includegraphics[scale=.5]{./fotos/cenario01_sig_phi_tp.png}
	\end{minipage}
		
\end{frame}

%% ---------------------------------------------------------------------------
\begin{frame}{Simulações e Resultados: Cenário 1 - KF}
	\centering
	\hspace*{.0cm}\includegraphics[scale=.45]{./fotos/TikZ_pgfPlots_errors_KF_cenario1.pdf}
	
	\hspace*{.0cm}\includegraphics[scale=.35]{./fotos/matlab_fig_decisao_KF.png}
\end{frame}

%% ---------------------------------------------------------------------------
\begin{frame}{Simulações e Resultados: Cenário 1 - PDNKF}
	\centering
	\hspace*{.0cm}\includegraphics[scale=.45]{./fotos/TikZ_pgfPlots_errors_PDNKF_cenario1.pdf}
	
	\hspace*{.0cm}\includegraphics[scale=.35]{./fotos/fig_decisao.png}
\end{frame}

%% ---------------------------------------------------------------------------
\begin{frame}{Simulações e Resultados: Cenário 1 - EKF}
	\centering
	\hspace*{.0cm}\includegraphics[scale=.45]{./fotos/TikZ_pgfPlots_errors_EKF_cenario1.pdf}
	
	\vspace*{-.05cm}
	\hspace*{.0cm}\includegraphics[scale=.35]{./fotos/matlab_fig_decisao_EKF.png}
\end{frame}

%% ---------------------------------------------------------------------------
\begin{frame}{Simulações e Resultados: Cenário 1 - UKF}
	\centering
	\hspace*{.0cm}\includegraphics[scale=.45]{./fotos/TikZ_pgfPlots_errors_UKF_cenario1.pdf}
	
	\hspace*{.0cm}\includegraphics[scale=.35]{./fotos/matlab_fig_decisao_UKF.png}
\end{frame}


%% ---------------------------------------------------------------------------
\subsection{Cenário 2}
\begin{frame}{Simulações e Resultados: Cenário 2}
	\begin{alertblock}{\textbf{Cenário 2}}
		\scriptsize
		$t_{p \text{ min}}=35$\unit{ms} e $f_{c \text{ min}}=14$\unit{kHz}, $a_f = 1,\!25e\!-\!4$, $a_t = 9,\!1$, $b_f = 10$ e $b_t = 10$, para $\sigma_r$ (dado em metros), e $a_f = 5,\!6e\!-\!5$, $a_t = 4,\!4$, $b_f = 1,\!5$ e $b_t = 1,\!5$, para $\sigma_{\phi}$ (dado em graus). O \bizu{SSAM convencional}: $f_{c0} = 30$\unit{kHz}, $t_{p0} = 150$\unit{ms}.
	\end{alertblock}
	\vspace*{.5cm}
	\hspace*{-1.cm}
	\begin{minipage}{0.57\textwidth}
		\includegraphics[scale=.5]{./fotos/cenario03_sig_r_fc.png}
	\end{minipage}
	\begin{minipage}{0.5\textwidth}
		\includegraphics[scale=.5]{./fotos/cenario03_sig_r_tp.png}
	\end{minipage}
	
	\hspace*{-1.cm}
	\begin{minipage}{0.57\textwidth}
		\includegraphics[scale=.5]{./fotos/cenario03_sig_phi_fc.png}
	\end{minipage}
	\begin{minipage}{0.5\textwidth}
		\includegraphics[scale=.5]{./fotos/cenario03_sig_phi_tp.png}
	\end{minipage}
	
\end{frame}

%% ---------------------------------------------------------------------------
\begin{frame}{Simulações e Resultados: Cenário 2 - KF}
	\centering
	\hspace*{.0cm}\includegraphics[scale=.45]{./fotos/TikZ_pgfPlots_errors_KF_cenario3.pdf}
	
	\hspace*{.0cm}\includegraphics[scale=.35]{./fotos/matlab_fig_decisao_KF_cenario3.png}
\end{frame}

%% ---------------------------------------------------------------------------
\begin{frame}{Simulações e Resultados: Cenário 2 - PDNKF}
	\centering
	\hspace*{.0cm}\includegraphics[scale=.45]{./fotos/TikZ_pgfPlots_errors_PDNKF_cenario3.pdf}
	
	\hspace*{.0cm}\includegraphics[scale=.35]{./fotos/matlab_fig_decisao_PDNKF_cenario3.png}
\end{frame}

%% ---------------------------------------------------------------------------
\begin{frame}{Simulações e Resultados: Cenário 2 - EKF}
	\centering
	\hspace*{.0cm}\includegraphics[scale=.45]{./fotos/TikZ_pgfPlots_errors_EKF_cenario3.pdf}
	
	\hspace*{.0cm}\includegraphics[scale=.35]{./fotos/matlab_fig_decisao_EKF_cenario3.png}
\end{frame}

%% ---------------------------------------------------------------------------
\begin{frame}{Simulações e Resultados: Cenário 2 - UKF}
	\centering
	\hspace*{.0cm}\includegraphics[scale=.45]{./fotos/TikZ_pgfPlots_errors_UKF_cenario3.pdf}
	
	\hspace*{.0cm}\includegraphics[scale=.35]{./fotos/matlab_fig_decisao_UKF_cenario3.png}
\end{frame}

%% ---------------------------------------------------------------------------
\begin{frame}{Simulações e Resultados: Resumo}
		\centering
%			\begin{tabular}{|c|c|c|c|c|}
%				\multicolumn{5}{c}{Processed time of an entire realization [s]}\\
%				$\alpha$-$\beta$ 	& KF 	& EKF 	& UKF 	& PDNKF\\
%				\hline
%				\makecell{$0.022\pm$\\$0.003$} &%
%				\makecell{$0.027\pm$\\$0.002$} &%
%				\makecell{$0.236\pm$\\$0.337$}	&%
%				\makecell{$2.576\pm$\\$0.337$}	&%
%				\makecell{$0.051\pm$\\$0.008$}\\
%				\hline		
%			\end{tabular}
\begin{table}[H]
	\centering
	\footnotesize
	\begin{tabular}{c|c|c}
		\textbf{CAS} & \textbf{RMSE médio posição} \unit{dB} & \textbf{RMSE médio velocidade} \unit{dB} \\
		\hline
		KF & 5 & 4 \\
		PDNKF & 5 & 2 \\
		EKF & 5 & 1 \\
		UKF & 5 & 1
	\end{tabular}
\end{table}
\begin{table}[H]
	\centering
	\footnotesize
	\begin{tabular}{c|c|c}
		\textbf{CAS} & \textbf{RMSE médio posição} \unit{dB} & \textbf{RMSE médio velocidade} \unit{dB} \\
		\hline
		KF & 2 & 1\\
		PDNKF & 2 & 0\\
		EKF & 2 & 0\\
		UKF & 2 & 0
	\end{tabular}
\end{table}

\begin{exampleblock}{Verificações Importantes}
	\begin{itemize}
		\item Estimativas de estado $\bm{\hat{x}}[n\!\ |\!\ n]$ mais precisas
		\begin{itemize}
			\item Aumento o desempenho do SSAM ($\downarrow \bm{\nu_y}[n\!\ |\!\ \bm{\theta}] \Rightarrow \uparrow \text{SNR}$)
		\end{itemize}
		\item Característica Adaptativa ao SSAM Convencional.
	\end{itemize}
\end{exampleblock}

\end{frame}

%% ---------------------------------------------------------------------------
\section{Produções Acadêmicas}
\begin{frame}{IEEE - OCEANS Conference 2023 - Limerick}
		\begin{overlayarea}{\textwidth}{.45\textheight}
		\centering
		\invisible<2-|handout:1>{
			\begin{multicols}{2}
				\tableofcontents[
				sectionstyle=show/shaded,
				subsectionstyle=show/shaded,
				subsubsectionstyle=show/shaded
				]
			\end{multicols}
		}
	\end{overlayarea}%
	\begin{overlayarea}{\textwidth}{1.0\textheight}
		\centering
		\visible<2-2>{
			\vspace{-4.2cm}
			\centering
			\footnotesize
			\begin{itemize}
				\item Evento internacional focado em Engenharia Oceânica, Tecnologia Marinha \& SONAR e tópicos relacionados.
				\item Marine Technology Society (MTS); IEEE Oceanic Engineering Society (OES).
				\item Kalman Filtering with Position-Dependent Noise Modeling for Active Sonar - \citet{alcantara_oceans}
			\end{itemize}
				
			
			
			\includegraphics[width=\textwidth]{./fotos/foto_alcantara.jpg}
		}
	\end{overlayarea}

\end{frame}

%% ---------------------------------------------------------------------------

\section{Conclusões e Trabalhos Futuros}
\begin{frame}{Conclusões}
	\begin{overlayarea}{\textwidth}{.45\textheight}
		\centering
		\invisible<2-|handout:1>{
			\begin{multicols}{2}
			\tableofcontents[
			sectionstyle=show/shaded,
			subsectionstyle=show/shaded,
			subsubsectionstyle=show/shaded
			]
		\end{multicols}
		}
	\end{overlayarea}%
	\begin{overlayarea}{\textwidth}{1.0\textheight}
		\centering
		\visible<2-3>{
			\vspace{-4.2cm}
			\vspace*{-.2cm}
			\begin{block}{Relative Mean Squared Error (RMSE)}
				\scriptsize
				\begin{itemize}
					\item Cognitivo: Adaptabilidade e Percepção - possibilita aumentar precisão da estimativa.
%					\begin{itemize}
%						\scriptsize
%						\item Estimativas mais precisas, maior a efiência de engajamento de Sistemas de Combate
%					\end{itemize}
					\item Filtro mais simples (KF) converge mais rápido
					\item PDNKF com problemas de convergência inicial.
				\end{itemize}
			\end{block}
			\vspace*{-.3cm}
			\begin{alertblock}{Aplicações}
				\scriptsize
				\begin{itemize}
					\item Anti-Submarine Warfare (ASW)
					\item Vigilância ativa em portos
				\end{itemize}
			\end{alertblock}
		\pause
		\pause
		\pause
			\begin{exampleblock}{\textbf{Trabalhos Futuros}}
				\scriptsize
				\begin{itemize}
					\item Investigar PDNKF
					\item Investigar mais funções $g(\cdot)$
					\item $\mathcal{P}_{\bm{\theta}}$ dimensionalmente maior (+ parâmetros)
					\item ADP por Q-Learning / Deep Q-Learning (model-free)
					\item ADP para $L>1$ ($L=2$)
					\item Modelos de Acústica Submarina mais verossímeis
					\item Arquiteturas com Memória e Mecanismo de Atenção
				\end{itemize}
			\end{exampleblock}
		}
	\end{overlayarea}
\end{frame}

%% ---------------------------------------------------------------------------


%% ---------------------------------------------------------------------------
% Final frame
%\addcontentsline{toc}{subsection}{Referências}
\section{Referências}
\begin{frame}[allowframebreaks]{Referências}
%    \centering
%    \huge{\textbf{\example{Obrigado(a) pela Atenção!}}}
    
    
%    \vspace{1cm}
%    
%    \Large{\textbf{Contato:}}
%    \newline
%    \vspace*{0.5cm}
%    \large{\makecell{\email{<EMAIL>}\\
%    		\email{<EMAIL>}\\
%    		\email{<EMAIL>}\\
%    		\email{<EMAIL>}}
%    	}
   \printbibliography[heading=none]
\end{frame}

%% ---------------------------------------------------------------------------
% Final frame
%\addcontentsline{toc}{section}{Referências}
%\section{Referências}
\begin{frame}{Fim}
	    \centering
	    \huge{\textbf{\example{Obrigado pela Atenção!}}}
	
	
	    \vspace{1cm}
	    
	    \large{\makecell{\textbf{Caio C.M.P. de Alcântara}\\ \email{<EMAIL>}}}
	    \newline
	    \vspace*{0.5cm}
	    % \large{\makecell{\email{<EMAIL>}\\
		% 	    		\email{<EMAIL>}\\
		% 	    		\email{<EMAIL>}\\
		% 	    		\email{<EMAIL>}\\
		% 	    		\email{<EMAIL>}\\
		% 	    		\email{<EMAIL>}}
		%     	}
%		\large{\makecell{\email{<EMAIL>}}
%		}
%	\printbibliography[heading=none]
\end{frame}

%% ---------------------------------------------------------------------------
\subsection{Backup}


\end{document}