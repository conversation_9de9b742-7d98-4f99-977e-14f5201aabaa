# Makefile for LaTeX Project
# Automatically detects main .tex files and compiles them to PDF
# Supports bibliography compilation with biber/bibtex
# Cross-platform compatible (Windows/Linux/macOS)

# Configuration
SHELL := /bin/bash
.PHONY: all clean distclean view help install-deps check-deps list-targets

# Auto-detect main .tex files (exclude files in libs/ directory)
TEX_FILES := $(shell find . -maxdepth 1 -name "*.tex" -type f | grep -v "libs/")
MAIN_TEX := $(firstword $(TEX_FILES))
PDF_FILES := $(TEX_FILES:.tex=.pdf)
TARGET_PDF := $(MAIN_TEX:.tex=.pdf)

# LaTeX engine preference order (xelatex preferred as requested)
LATEX_ENGINES := xelatex pdflatex lualatex
LATEX_ENGINE := $(shell for engine in $(LATEX_ENGINES); do \
	if command -v $$engine >/dev/null 2>&1; then \
		echo $$engine; \
		break; \
	fi; \
done)

# Bibliography tools preference (biber preferred for presentations as requested)
BIB_TOOLS := biber bibtex
BIB_TOOL := $(shell for tool in $(BIB_TOOLS); do \
	if command -v $$tool >/dev/null 2>&1; then \
		echo $$tool; \
		break; \
	fi; \
done)

# Check if bibliography files exist
BIB_FILES := $(wildcard *.bib configs/*.bib)
HAS_BIB := $(if $(BIB_FILES),yes,no)

# Platform detection for PDF viewer
UNAME_S := $(shell uname -s 2>/dev/null || echo "Windows")
ifeq ($(UNAME_S),Linux)
	PDF_VIEWER := xdg-open
else ifeq ($(UNAME_S),Darwin)
	PDF_VIEWER := open
else
	PDF_VIEWER := start
endif

# Auxiliary file extensions to clean
AUX_EXTS := aux log toc out nav snm vrb fls fdb_latexmk synctex.gz \
           bbl blg bcf run.xml lof lot lol idx ind ilg glo gls glg \
           acn acr alg ist figlist makefile.fls makefile.fdb_latexmk

# Colors for output (if terminal supports it)
ifneq (,$(shell command -v tput 2>/dev/null))
	RED := $(shell tput setaf 1)
	GREEN := $(shell tput setaf 2)
	YELLOW := $(shell tput setaf 3)
	BLUE := $(shell tput setaf 4)
	RESET := $(shell tput sgr0)
else
	RED :=
	GREEN :=
	YELLOW :=
	BLUE :=
	RESET :=
endif

# Default target
all: check-deps $(TARGET_PDF)

# Help target
help:
	@echo "$(BLUE)LaTeX Project Makefile$(RESET)"
	@echo "Available targets:"
	@echo "  $(GREEN)all$(RESET)        - Compile the main document (default)"
	@echo "  $(GREEN)clean$(RESET)      - Remove auxiliary files"
	@echo "  $(GREEN)distclean$(RESET)  - Remove all generated files including PDFs"
	@echo "  $(GREEN)view$(RESET)       - Open the compiled PDF"
	@echo "  $(GREEN)check-deps$(RESET) - Check if required tools are installed"
	@echo "  $(GREEN)list-targets$(RESET) - List detected .tex files and targets"
	@echo "  $(GREEN)help$(RESET)       - Show this help message"
	@echo ""
	@echo "$(YELLOW)Configuration:$(RESET)"
	@echo "  Main document: $(MAIN_TEX)"
	@echo "  LaTeX engine:  $(if $(LATEX_ENGINE),$(LATEX_ENGINE),$(RED)NOT FOUND$(RESET))"
	@echo "  Bibliography:  $(if $(BIB_TOOL),$(BIB_TOOL),$(RED)NOT FOUND$(RESET)) ($(HAS_BIB))"
	@echo "  PDF viewer:    $(PDF_VIEWER)"

# Check dependencies
check-deps:
	@echo "$(BLUE)Checking dependencies...$(RESET)"
	@if [ -z "$(LATEX_ENGINE)" ]; then \
		echo "$(RED)Error: No LaTeX engine found!$(RESET)"; \
		echo "Please install one of: $(LATEX_ENGINES)"; \
		exit 1; \
	else \
		echo "$(GREEN)✓$(RESET) LaTeX engine: $(LATEX_ENGINE)"; \
	fi
	@if [ "$(HAS_BIB)" = "yes" ] && [ -z "$(BIB_TOOL)" ]; then \
		echo "$(YELLOW)Warning: Bibliography files found but no bibliography tool available$(RESET)"; \
		echo "Please install one of: $(BIB_TOOLS)"; \
	elif [ "$(HAS_BIB)" = "yes" ]; then \
		echo "$(GREEN)✓$(RESET) Bibliography tool: $(BIB_TOOL)"; \
	fi

# List detected targets
list-targets:
	@echo "$(BLUE)Detected .tex files:$(RESET)"
	@for file in $(TEX_FILES); do echo "  $$file"; done
	@echo "$(BLUE)Target PDFs:$(RESET)"
	@for file in $(PDF_FILES); do echo "  $$file"; done

# Main compilation rule
%.pdf: %.tex
	@echo "$(BLUE)Compiling $< to $@...$(RESET)"
	@if [ -z "$(LATEX_ENGINE)" ]; then \
		echo "$(RED)Error: No LaTeX engine available$(RESET)"; \
		exit 1; \
	fi
	
	# First compilation
	@echo "$(YELLOW)First pass...$(RESET)"
	$(LATEX_ENGINE) -interaction=nonstopmode -halt-on-error "$<" || \
		(echo "$(RED)LaTeX compilation failed!$(RESET)"; exit 1)
	
	# Bibliography compilation if needed
	@if [ "$(HAS_BIB)" = "yes" ] && [ -n "$(BIB_TOOL)" ]; then \
		echo "$(YELLOW)Processing bibliography...$(RESET)"; \
		$(BIB_TOOL) "$*" || echo "$(YELLOW)Bibliography processing failed (continuing)$(RESET)"; \
	fi
	
	# Second compilation (for references)
	@echo "$(YELLOW)Second pass...$(RESET)"
	$(LATEX_ENGINE) -interaction=nonstopmode -halt-on-error "$<" || \
		(echo "$(RED)LaTeX compilation failed!$(RESET)"; exit 1)
	
	# Third compilation (for bibliography and cross-references)
	@if [ "$(HAS_BIB)" = "yes" ] || grep -q "\\\\ref{" "$<" 2>/dev/null; then \
		echo "$(YELLOW)Third pass...$(RESET)"; \
		$(LATEX_ENGINE) -interaction=nonstopmode -halt-on-error "$<" || \
			(echo "$(RED)LaTeX compilation failed!$(RESET)"; exit 1); \
	fi
	
	@echo "$(GREEN)✓ Successfully compiled $< to $@$(RESET)"

# View the compiled PDF
view: $(TARGET_PDF)
	@echo "$(BLUE)Opening $(TARGET_PDF)...$(RESET)"
	@if [ -f "$(TARGET_PDF)" ]; then \
		$(PDF_VIEWER) "$(TARGET_PDF)" 2>/dev/null & \
	else \
		echo "$(RED)Error: $(TARGET_PDF) not found. Run 'make' first.$(RESET)"; \
		exit 1; \
	fi

# Clean auxiliary files
clean:
	@echo "$(BLUE)Cleaning auxiliary files...$(RESET)"
	@for ext in $(AUX_EXTS); do \
		find . -name "*.$$ext" -type f -delete 2>/dev/null || true; \
	done
	@echo "$(GREEN)✓ Auxiliary files cleaned$(RESET)"

# Clean all generated files including PDFs
distclean: clean
	@echo "$(BLUE)Cleaning all generated files...$(RESET)"
	@for pdf in $(PDF_FILES); do \
		if [ -f "$$pdf" ]; then \
			rm -f "$$pdf"; \
			echo "  Removed $$pdf"; \
		fi; \
	done
	@echo "$(GREEN)✓ All generated files cleaned$(RESET)"

# Force rebuild
rebuild: distclean all

# Install common LaTeX dependencies (platform-specific suggestions)
install-deps:
	@echo "$(BLUE)LaTeX Installation Suggestions:$(RESET)"
	@echo ""
	@echo "$(YELLOW)Windows:$(RESET)"
	@echo "  - Install MiKTeX: https://miktex.org/"
	@echo "  - Or install TeX Live: https://tug.org/texlive/"
	@echo ""
	@echo "$(YELLOW)macOS:$(RESET)"
	@echo "  - Install MacTeX: brew install --cask mactex"
	@echo "  - Or: brew install --cask basictex"
	@echo ""
	@echo "$(YELLOW)Linux (Ubuntu/Debian):$(RESET)"
	@echo "  - sudo apt-get install texlive-full"
	@echo "  - Or minimal: sudo apt-get install texlive-latex-base texlive-latex-extra"
	@echo ""
	@echo "$(YELLOW)Linux (Fedora/RHEL):$(RESET)"
	@echo "  - sudo dnf install texlive-scheme-full"
	@echo ""
	@echo "$(YELLOW)Arch Linux:$(RESET)"
	@echo "  - sudo pacman -S texlive-most texlive-bibtexextra"

# Debug information
debug:
	@echo "$(BLUE)Debug Information:$(RESET)"
	@echo "TEX_FILES: $(TEX_FILES)"
	@echo "MAIN_TEX: $(MAIN_TEX)"
	@echo "PDF_FILES: $(PDF_FILES)"
	@echo "TARGET_PDF: $(TARGET_PDF)"
	@echo "LATEX_ENGINE: $(LATEX_ENGINE)"
	@echo "BIB_TOOL: $(BIB_TOOL)"
	@echo "BIB_FILES: $(BIB_FILES)"
	@echo "HAS_BIB: $(HAS_BIB)"
	@echo "UNAME_S: $(UNAME_S)"
	@echo "PDF_VIEWER: $(PDF_VIEWER)"
