# Makefile for LaTeX Project
# Automatically detects main .tex files and compiles them to PDF
# Supports bibliography compilation with biber/bibtex
# Cross-platform compatible (Windows/Linux/macOS)

# Configuration
SHELL := /bin/bash
.PHONY: all clean distclean view help install-deps check-deps list-targets quick watch rebuild debug diagnose

# Auto-detect main .tex files (exclude files in libs/ directory)
TEX_FILES := $(shell find . -maxdepth 1 -name "*.tex" -type f | grep -v "libs/")
MAIN_TEX := $(firstword $(TEX_FILES))
PDF_FILES := $(TEX_FILES:.tex=.pdf)
TARGET_PDF := $(MAIN_TEX:.tex=.pdf)

# LaTeX engine preference order (xelatex strongly preferred for this project)
LATEX_ENGINES := xelatex pdflatex lualatex
LATEX_ENGINE := $(shell for engine in $(LATEX_ENGINES); do \
	if command -v $$engine >/dev/null 2>&1; then \
		echo $$engine; \
		break; \
	fi; \
done)

# Bibliography tools preference (biber strongly preferred for modern LaTeX)
BIB_TOOLS := biber bibtex
BIB_TOOL := $(shell for tool in $(BIB_TOOLS); do \
	if command -v $$tool >/dev/null 2>&1; then \
		echo $$tool; \
		break; \
	fi; \
done)

# XeLaTeX-specific flags for better Unicode and font handling
XELATEX_FLAGS := -interaction=nonstopmode -halt-on-error -shell-escape
PDFLATEX_FLAGS := -interaction=nonstopmode -halt-on-error
LUALATEX_FLAGS := -interaction=nonstopmode -halt-on-error -shell-escape

# Get appropriate flags based on engine
LATEX_FLAGS := $(if $(filter xelatex,$(LATEX_ENGINE)),$(XELATEX_FLAGS),\
	$(if $(filter pdflatex,$(LATEX_ENGINE)),$(PDFLATEX_FLAGS),$(LUALATEX_FLAGS)))

# Check if bibliography files exist and determine bibliography method
BIB_FILES := $(wildcard *.bib configs/*.bib)
HAS_BIB := $(if $(BIB_FILES),yes,no)

# Check if document uses modern bibliography commands (for Biber)
USES_BIBLATEX := $(shell if [ -f "$(MAIN_TEX)" ] && grep -q "\\\\addbibresource\|\\\\printbibliography" "$(MAIN_TEX)" 2>/dev/null; then echo "yes"; else echo "no"; fi)

# Determine optimal bibliography workflow
BIB_WORKFLOW := $(if $(filter yes,$(USES_BIBLATEX)),biblatex,traditional)

# Platform detection for PDF viewer
UNAME_S := $(shell uname -s 2>/dev/null || echo "Windows")
ifeq ($(UNAME_S),Linux)
	PDF_VIEWER := xdg-open
else ifeq ($(UNAME_S),Darwin)
	PDF_VIEWER := open
else
	PDF_VIEWER := start
endif

# Auxiliary file extensions to clean (expanded for XeLaTeX and Biber)
AUX_EXTS := aux log toc out nav snm vrb fls fdb_latexmk synctex.gz \
           bbl blg bcf run.xml lof lot lol idx ind ilg glo gls glg \
           acn acr alg ist figlist makefile.fls makefile.fdb_latexmk \
           xdv fdb_latexmk.fls auxlock

# Colors for output (if terminal supports it)
ifneq (,$(shell command -v tput 2>/dev/null))
	RED := $(shell tput setaf 1)
	GREEN := $(shell tput setaf 2)
	YELLOW := $(shell tput setaf 3)
	BLUE := $(shell tput setaf 4)
	RESET := $(shell tput sgr0)
else
	RED :=
	GREEN :=
	YELLOW :=
	BLUE :=
	RESET :=
endif

# Default target
all: check-deps $(TARGET_PDF)

# Help target
help:
	@echo "$(BLUE)LaTeX Project Makefile (XeLaTeX/Biber Optimized)$(RESET)"
	@echo "Available targets:"
	@echo "  $(GREEN)all$(RESET)        - Compile the main document (default, full compilation)"
	@echo "  $(GREEN)quick$(RESET)      - Quick single-pass compilation (for drafts)"
	@echo "  $(GREEN)watch$(RESET)      - Continuous compilation mode (watch for changes)"
	@echo "  $(GREEN)clean$(RESET)      - Remove auxiliary files"
	@echo "  $(GREEN)distclean$(RESET)  - Remove all generated files including PDFs"
	@echo "  $(GREEN)rebuild$(RESET)    - Clean and rebuild everything"
	@echo "  $(GREEN)view$(RESET)       - Open the compiled PDF"
	@echo "  $(GREEN)check-deps$(RESET) - Check if required tools are installed"
	@echo "  $(GREEN)list-targets$(RESET) - List detected .tex files and targets"
	@echo "  $(GREEN)diagnose$(RESET)   - Diagnose potential compilation issues"
	@echo "  $(GREEN)debug$(RESET)      - Show debug information"
	@echo "  $(GREEN)install-deps$(RESET) - Show installation suggestions"
	@echo "  $(GREEN)help$(RESET)       - Show this help message"
	@echo ""
	@echo "$(YELLOW)Configuration:$(RESET)"
	@echo "  Main document: $(MAIN_TEX)"
	@echo "  LaTeX engine:  $(if $(LATEX_ENGINE),$(LATEX_ENGINE),$(RED)NOT FOUND$(RESET))"
	@echo "  LaTeX flags:   $(LATEX_FLAGS)"
	@echo "  Bibliography:  $(if $(BIB_TOOL),$(BIB_TOOL),$(RED)NOT FOUND$(RESET)) ($(HAS_BIB))"
	@echo "  Bib workflow:  $(BIB_WORKFLOW)"
	@echo "  PDF viewer:    $(PDF_VIEWER)"

# Check dependencies with enhanced XeLaTeX/Biber validation
check-deps:
	@echo "$(BLUE)Checking dependencies...$(RESET)"
	@if [ -z "$(LATEX_ENGINE)" ]; then \
		echo "$(RED)Error: No LaTeX engine found!$(RESET)"; \
		echo "Please install one of: $(LATEX_ENGINES)"; \
		echo "$(YELLOW)Recommended: Install TeX Live with XeLaTeX support$(RESET)"; \
		exit 1; \
	else \
		echo "$(GREEN)✓$(RESET) LaTeX engine: $(LATEX_ENGINE)"; \
		if [ "$(LATEX_ENGINE)" != "xelatex" ]; then \
			echo "$(YELLOW)Warning: XeLaTeX is recommended for this project$(RESET)"; \
		fi; \
	fi
	@if [ "$(HAS_BIB)" = "yes" ] && [ -z "$(BIB_TOOL)" ]; then \
		echo "$(YELLOW)Warning: Bibliography files found but no bibliography tool available$(RESET)"; \
		echo "Please install one of: $(BIB_TOOLS)"; \
		echo "$(YELLOW)Recommended: Install Biber for modern bibliography processing$(RESET)"; \
	elif [ "$(HAS_BIB)" = "yes" ]; then \
		echo "$(GREEN)✓$(RESET) Bibliography tool: $(BIB_TOOL) (workflow: $(BIB_WORKFLOW))"; \
		if [ "$(BIB_TOOL)" = "bibtex" ] && [ "$(BIB_WORKFLOW)" = "biblatex" ]; then \
			echo "$(YELLOW)Warning: Document may require Biber instead of BibTeX$(RESET)"; \
		fi; \
	fi
	@echo "$(BLUE)Checking project-specific requirements...$(RESET)"
	@if [ ! -f "libs/ufc_format.cls" ]; then \
		echo "$(RED)Error: Custom document class libs/ufc_format.cls not found$(RESET)"; \
		exit 1; \
	else \
		echo "$(GREEN)✓$(RESET) Custom document class found"; \
	fi
	@if [ ! -f "libs/preamble.tex" ]; then \
		echo "$(RED)Error: Preamble file libs/preamble.tex not found$(RESET)"; \
		exit 1; \
	else \
		echo "$(GREEN)✓$(RESET) Preamble file found"; \
	fi

# List detected targets
list-targets:
	@echo "$(BLUE)Detected .tex files:$(RESET)"
	@for file in $(TEX_FILES); do echo "  $$file"; done
	@echo "$(BLUE)Target PDFs:$(RESET)"
	@for file in $(PDF_FILES); do echo "  $$file"; done

# Main compilation rule optimized for XeLaTeX/Biber workflow
%.pdf: %.tex
	@echo "$(BLUE)Compiling $< to $@ using $(LATEX_ENGINE)...$(RESET)"
	@if [ -z "$(LATEX_ENGINE)" ]; then \
		echo "$(RED)Error: No LaTeX engine available$(RESET)"; \
		exit 1; \
	fi

	# Clean previous auxiliary files for fresh compilation
	@echo "$(YELLOW)Cleaning previous auxiliary files...$(RESET)"
	@for ext in aux bbl bcf blg run.xml; do \
		rm -f "$*.$$ext" 2>/dev/null || true; \
	done

	# First compilation pass
	@echo "$(YELLOW)First pass ($(LATEX_ENGINE))...$(RESET)"
	$(LATEX_ENGINE) $(LATEX_FLAGS) "$<" || \
		(echo "$(RED)LaTeX first pass failed!$(RESET)"; \
		 echo "$(YELLOW)Check the log file: $*.log$(RESET)"; exit 1)

	# Bibliography compilation if needed
	@if [ "$(HAS_BIB)" = "yes" ] && [ -n "$(BIB_TOOL)" ]; then \
		echo "$(YELLOW)Processing bibliography with $(BIB_TOOL)...$(RESET)"; \
		if [ "$(BIB_TOOL)" = "biber" ]; then \
			biber "$*" || (echo "$(RED)Biber failed!$(RESET)"; \
			echo "$(YELLOW)Check bibliography file and citations$(RESET)"; exit 1); \
		else \
			bibtex "$*" || (echo "$(YELLOW)BibTeX failed (continuing)$(RESET)"); \
		fi; \
	fi

	# Second compilation pass (incorporate bibliography)
	@echo "$(YELLOW)Second pass (incorporating bibliography)...$(RESET)"
	$(LATEX_ENGINE) $(LATEX_FLAGS) "$<" || \
		(echo "$(RED)LaTeX second pass failed!$(RESET)"; exit 1)

	# Third compilation pass (resolve all references)
	@echo "$(YELLOW)Third pass (resolving cross-references)...$(RESET)"
	$(LATEX_ENGINE) $(LATEX_FLAGS) "$<" || \
		(echo "$(RED)LaTeX third pass failed!$(RESET)"; exit 1)

	# Fourth pass if needed (for complex documents with many references)
	@if grep -q "Rerun to get cross-references right\|There were undefined references" "$*.log" 2>/dev/null; then \
		echo "$(YELLOW)Fourth pass (final reference resolution)...$(RESET)"; \
		$(LATEX_ENGINE) $(LATEX_FLAGS) "$<" || \
			(echo "$(RED)LaTeX fourth pass failed!$(RESET)"; exit 1); \
	fi

	@echo "$(GREEN)✓ Successfully compiled $< to $@$(RESET)"
	@if [ -f "$*.log" ]; then \
		if grep -q "Warning" "$*.log"; then \
			echo "$(YELLOW)⚠ Compilation completed with warnings. Check $*.log$(RESET)"; \
		fi; \
	fi

# View the compiled PDF
view: $(TARGET_PDF)
	@echo "$(BLUE)Opening $(TARGET_PDF)...$(RESET)"
	@if [ -f "$(TARGET_PDF)" ]; then \
		$(PDF_VIEWER) "$(TARGET_PDF)" 2>/dev/null & \
	else \
		echo "$(RED)Error: $(TARGET_PDF) not found. Run 'make' first.$(RESET)"; \
		exit 1; \
	fi

# Clean auxiliary files
clean:
	@echo "$(BLUE)Cleaning auxiliary files...$(RESET)"
	@for ext in $(AUX_EXTS); do \
		find . -name "*.$$ext" -type f -delete 2>/dev/null || true; \
	done
	@echo "$(GREEN)✓ Auxiliary files cleaned$(RESET)"

# Clean all generated files including PDFs
distclean: clean
	@echo "$(BLUE)Cleaning all generated files...$(RESET)"
	@for pdf in $(PDF_FILES); do \
		if [ -f "$$pdf" ]; then \
			rm -f "$$pdf"; \
			echo "  Removed $$pdf"; \
		fi; \
	done
	@echo "$(GREEN)✓ All generated files cleaned$(RESET)"

# Force rebuild
rebuild: distclean all

# Quick compilation (single pass, for draft checking)
quick: check-deps
	@echo "$(BLUE)Quick compilation (single pass)...$(RESET)"
	@if [ -z "$(LATEX_ENGINE)" ]; then \
		echo "$(RED)Error: No LaTeX engine available$(RESET)"; \
		exit 1; \
	fi
	$(LATEX_ENGINE) $(LATEX_FLAGS) "$(MAIN_TEX)" || \
		(echo "$(RED)Quick compilation failed!$(RESET)"; exit 1)
	@echo "$(GREEN)✓ Quick compilation completed$(RESET)"
	@echo "$(YELLOW)Note: Bibliography and cross-references may not be updated$(RESET)"

# Continuous compilation mode (watch for changes)
watch:
	@echo "$(BLUE)Starting continuous compilation mode...$(RESET)"
	@echo "$(YELLOW)Press Ctrl+C to stop$(RESET)"
	@while true; do \
		if [ "$(MAIN_TEX)" -nt "$(TARGET_PDF)" ] || \
		   find libs/ fotos/ configs/ -newer "$(TARGET_PDF)" 2>/dev/null | grep -q .; then \
			echo "$(BLUE)Changes detected, recompiling...$(RESET)"; \
			$(MAKE) quick; \
		fi; \
		sleep 2; \
	done

# Install common LaTeX dependencies (platform-specific suggestions)
install-deps:
	@echo "$(BLUE)LaTeX Installation Suggestions for XeLaTeX/Biber:$(RESET)"
	@echo ""
	@echo "$(YELLOW)Windows:$(RESET)"
	@echo "  - Install MiKTeX: https://miktex.org/"
	@echo "  - Or install TeX Live: https://tug.org/texlive/"
	@echo "  - Ensure XeLaTeX and Biber are included"
	@echo ""
	@echo "$(YELLOW)macOS:$(RESET)"
	@echo "  - Install MacTeX: brew install --cask mactex"
	@echo "  - Or: brew install --cask basictex && tlmgr install xetex biber"
	@echo ""
	@echo "$(YELLOW)Linux (Ubuntu/Debian):$(RESET)"
	@echo "  - Full installation: sudo apt-get install texlive-full"
	@echo "  - Minimal: sudo apt-get install texlive-xetex texlive-bibtex-extra biber"
	@echo "  - Additional: sudo apt-get install texlive-fonts-extra texlive-lang-portuguese"
	@echo ""
	@echo "$(YELLOW)Linux (Fedora/RHEL):$(RESET)"
	@echo "  - sudo dnf install texlive-scheme-full"
	@echo "  - Or: sudo dnf install texlive-xetex texlive-biber"
	@echo ""
	@echo "$(YELLOW)Arch Linux:$(RESET)"
	@echo "  - sudo pacman -S texlive-most texlive-bibtexextra"
	@echo ""
	@echo "$(BLUE)Post-installation verification:$(RESET)"
	@echo "  - Run 'make check-deps' to verify installation"
	@echo "  - Test with 'make quick' for a fast compilation test"

# Diagnose compilation issues
diagnose:
	@echo "$(BLUE)Diagnosing potential compilation issues...$(RESET)"
	@echo ""
	@echo "$(YELLOW)Checking file structure:$(RESET)"
	@if [ ! -f "$(MAIN_TEX)" ]; then \
		echo "$(RED)✗$(RESET) Main document $(MAIN_TEX) not found"; \
	else \
		echo "$(GREEN)✓$(RESET) Main document found: $(MAIN_TEX)"; \
	fi
	@if [ ! -d "libs" ]; then \
		echo "$(RED)✗$(RESET) libs/ directory not found"; \
	else \
		echo "$(GREEN)✓$(RESET) libs/ directory found"; \
	fi
	@if [ ! -d "fotos" ]; then \
		echo "$(YELLOW)⚠$(RESET) fotos/ directory not found (may cause image errors)"; \
	else \
		echo "$(GREEN)✓$(RESET) fotos/ directory found"; \
	fi
	@echo ""
	@echo "$(YELLOW)Checking bibliography setup:$(RESET)"
	@if [ "$(HAS_BIB)" = "yes" ]; then \
		echo "$(GREEN)✓$(RESET) Bibliography files found: $(BIB_FILES)"; \
		if grep -q "\\\\addbibresource" "$(MAIN_TEX)" 2>/dev/null; then \
			echo "$(GREEN)✓$(RESET) Document uses modern biblatex commands"; \
		elif grep -q "\\\\bibliography" "$(MAIN_TEX)" 2>/dev/null; then \
			echo "$(YELLOW)⚠$(RESET) Document uses traditional bibliography commands"; \
		fi; \
	else \
		echo "$(YELLOW)⚠$(RESET) No bibliography files found"; \
	fi
	@echo ""
	@echo "$(YELLOW)Checking for common issues:$(RESET)"
	@if [ -f "$(MAIN_TEX)" ]; then \
		if grep -q "\\\\usepackage.*fontspec" "$(MAIN_TEX)" libs/*.tex 2>/dev/null; then \
			echo "$(GREEN)✓$(RESET) fontspec package detected (good for XeLaTeX)"; \
		else \
			echo "$(YELLOW)⚠$(RESET) fontspec package not detected (may be needed for XeLaTeX)"; \
		fi; \
		if grep -q "\\\\usepackage.*babel" "$(MAIN_TEX)" libs/*.tex 2>/dev/null; then \
			echo "$(GREEN)✓$(RESET) babel package detected"; \
		fi; \
	fi
	@if [ -f "$(TARGET_PDF:.pdf=.log)" ]; then \
		echo "$(BLUE)Recent compilation log analysis:$(RESET)"; \
		if grep -q "Error" "$(TARGET_PDF:.pdf=.log)"; then \
			echo "$(RED)✗$(RESET) Errors found in last compilation"; \
		fi; \
		if grep -q "Warning" "$(TARGET_PDF:.pdf=.log)"; then \
			echo "$(YELLOW)⚠$(RESET) Warnings found in last compilation"; \
		fi; \
	fi

# Debug information
debug:
	@echo "$(BLUE)Debug Information:$(RESET)"
	@echo "TEX_FILES: $(TEX_FILES)"
	@echo "MAIN_TEX: $(MAIN_TEX)"
	@echo "PDF_FILES: $(PDF_FILES)"
	@echo "TARGET_PDF: $(TARGET_PDF)"
	@echo "LATEX_ENGINE: $(LATEX_ENGINE)"
	@echo "LATEX_FLAGS: $(LATEX_FLAGS)"
	@echo "BIB_TOOL: $(BIB_TOOL)"
	@echo "BIB_FILES: $(BIB_FILES)"
	@echo "HAS_BIB: $(HAS_BIB)"
	@echo "USES_BIBLATEX: $(USES_BIBLATEX)"
	@echo "BIB_WORKFLOW: $(BIB_WORKFLOW)"
	@echo "UNAME_S: $(UNAME_S)"
	@echo "PDF_VIEWER: $(PDF_VIEWER)"
	@echo "AUX_EXTS: $(AUX_EXTS)"
